from contextlib import ExitStack
import json
from concurrent.futures import Thr<PERSON>PoolExecutor
from functools import partial
import boto3
import sys
import os
from datetime import datetime
import pymongo
from bson import ObjectId
import requests

# For local testing, add layer path
if os.environ.get('AWS_SAM_LOCAL'):
    sys.path.append('/opt/python')
    sys.path.append('/opt/python/python')
else:
    sys.path.append('/opt/python')
    sys.path.append('/opt/python/python')

from aegisGrader import run_grader_with_pdf_bytes, doRubric, doParallelAnswerSheet
from parallels import Manager

# Use default credential chain (AWS CLI for local, IAM role for Lambda)
s3_client = boto3.client('s3')
                        

# MongoDB setup
def get_mongo_client():
    # Use test DB if running locally, prod DB otherwise
    is_local = os.environ.get('AWS_SAM_LOCAL')
    env_var = 'MONGO_URI_TEST' if is_local else 'MONGO_URI_PROD'
    mongo_uri = os.environ.get(env_var)
    
    print(f"Environment: {'LOCAL' if is_local else 'PROD'}")
    print(f"Using environment variable: {env_var}")
    print(f"Connection string exists: {bool(mongo_uri)}")
    
    if not mongo_uri:
        raise ValueError(f"{env_var} environment variable not set")
    
    # Log connection string (masked for security)
    masked_uri = mongo_uri.replace(mongo_uri.split('@')[0].split('//')[1], '***:***') if '@' in mongo_uri else mongo_uri
    print(f"Connecting to: {masked_uri}")
    
    try:
        client = pymongo.MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        # Test the connection
        client.admin.command('ping')
        print("✅ MongoDB connection successful")
        return client
    except Exception as e:
        print(f"❌ MongoDB connection failed: {str(e)}")
        raise

def create_grading_document(manifest_data):
    """Create the initial grading document with empty answer sheets array"""
    try:
        print("Creating initial grading document...")
        client = get_mongo_client()

        is_local = os.environ.get('AWS_SAM_LOCAL')
        db_name = 'AegisScholarTestDb' if is_local else 'AegisScholarDb'
        print(f"Using database: {db_name}")

        db = client[db_name]
        collection = db['AegisGrader']

        # Extract files by purpose
        question_paper = None
        rubric = None

        for file in manifest_data.get('files', []):
            if file.get('filePurpose') == 'question_paper':
                question_paper = file
            elif file.get('filePurpose') == 'rubric':
                rubric = file

        # Get total answer sheets count for processing stats
        total_answer_sheets = len([f for f in manifest_data.get('files', []) if f.get('filePurpose') == 'answer_sheet'])

        # Create the main document with empty answer sheets array
        aegis_grader_doc = {
            "testDetails": {
                "createdBy": manifest_data['testDetails'].get('CreatedBy', ''),
                "className": manifest_data['testDetails'].get('className', ''),
                "subject": manifest_data['testDetails'].get('subject', ''),
                "date": manifest_data['testDetails'].get('date', datetime.now().isoformat())
            },
            "answerSheets": [],  # Empty array to start with
            "questionPaper": {
                "type": "questionPaper",
                "pdfUrl": question_paper.get('key', '') if question_paper else '',
                "timestamp": question_paper.get('timestamp') if question_paper else int(datetime.now().timestamp() * 1000)
            },
            "rubric": {
                "type": "rubric",
                "pdfUrl": rubric.get('key', '') if rubric else '',
                "timestamp": rubric.get('timestamp') if rubric else int(datetime.now().timestamp() * 1000)
            },
            "processingStats": {
                "totalAnswerSheets": total_answer_sheets,
                "successfulEvaluations": 0,
                "failedEvaluations": 0,
                "completedAt": None,
                "processingStartedAt": datetime.now(),
                "overallStatus": "processing"
            },
            "creditInfo": {
                "totalCreditsCharged": manifest_data.get('creditInfo', {}).get('totalCreditsCharged', 0),
                "creditsRefunded": manifest_data.get('creditInfo', {}).get('creditsRefunded', 0),
                "originalTransactionId": manifest_data.get('creditInfo', {}).get('originalTransactionId', ''),
                "refundTransactionIds": manifest_data.get('creditInfo', {}).get('refundTransactionIds', [])
            },
            "createdAt": datetime.now(),
            "updatedAt": datetime.now()
        }

        result = collection.insert_one(aegis_grader_doc)
        document_id = result.inserted_id
        print(f"Created grading document with ID: {document_id}")

        client.close()
        return document_id

    except Exception as e:
        print(f"Error creating grading document: {str(e)}")
        return None

def append_answer_sheet_to_document(document_id, manifest_data, evaluation_result):
    """Append a single answer sheet evaluation to the existing document"""
    try:
        if not evaluation_result or not document_id:
            return False

        print(f"Appending answer sheet to document {document_id}...")
        client = get_mongo_client()

        is_local = os.environ.get('AWS_SAM_LOCAL')
        db_name = 'AegisScholarTestDb' if is_local else 'AegisScholarDb'

        db = client[db_name]
        collection = db['AegisGrader']

        # evaluation_result is a tuple: (evaluation_text, answer_sheet_key)
        evaluation_text, answer_sheet_key = evaluation_result

        # Find the corresponding answer sheet file info
        answer_sheet_file = None
        for file in manifest_data.get('files', []):
            if file.get('filePurpose') == 'answer_sheet' and file.get('key') == answer_sheet_key:
                answer_sheet_file = file
                break

        if not answer_sheet_file:
            print(f"Could not find answer sheet file info for key: {answer_sheet_key}")
            client.close()
            return False

        # Determine if this is an error result
        is_error = evaluation_text.startswith("Error:") or evaluation_text.startswith("ERROR")

        # Create answer sheet document
        answer_sheet_doc = {
            "id": answer_sheet_file.get('key'),
            "studentName": answer_sheet_file.get('studentName', ''),
            "rollNumber": answer_sheet_file.get('rollNumber', ''),
            "pdfUrl": answer_sheet_file.get('key'),  # Using S3 key as URL reference
            "timestamp": answer_sheet_file.get('timestamp'),
            "evaluationResult": evaluation_text,
            "answerSheetKey": answer_sheet_key,
            "status": "error" if is_error else "completed",
            "processedAt": datetime.now().isoformat()
        }

        # Use $push to append the answer sheet to the existing document
        result = collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$push": {"answerSheets": answer_sheet_doc},
                "$set": {"updatedAt": datetime.now()}
            }
        )

        if result.modified_count > 0:
            print(f"Successfully appended answer sheet for student {answer_sheet_file.get('studentName', 'Unknown')}")
            client.close()
            return True
        else:
            print(f"Failed to append answer sheet - document not found or not modified")
            client.close()
            return False

    except Exception as e:
        print(f"Error appending answer sheet to MongoDB: {str(e)}")
        return False

def process_refunds_for_failed_sheets(credit_info, failed_sheet_count):
    """Call the Node.js backend to process refunds for failed answer sheets"""
    try:
        # Check if refunds are enabled
        refunds_enabled = os.environ.get('ENABLE_REFUNDS', 'true').lower() == 'true'
        if not refunds_enabled:
            print("💰 Refunds are disabled via ENABLE_REFUNDS environment variable")
            return None

        if failed_sheet_count <= 0:
            print("No failed sheets to refund")
            return None

        # Get the backend URL from environment variable
        is_local = os.environ.get('AWS_SAM_LOCAL')
        env_var = 'BACKEND_URL_TEST' if is_local else 'BACKEND_URL_PROD'
        backend_url = os.environ.get(env_var)
        if not backend_url:
            print(f"❌ {env_var} environment variable not set")
            return None

        # Log the backend URL for debugging
        print(f"🔗 Using backend URL: {backend_url}")
        print(f"🔗 Environment: {'LOCAL' if is_local else 'PRODUCTION'}")

        refund_endpoint = f"{backend_url}/api/aegisGrader/process-lambda-refunds"

        payload = {
            "creditInfo": credit_info,
            "failedSheetCount": failed_sheet_count
        }

        headers = {
            "Content-Type": "application/json",
            # Add any authentication headers if needed
            # "Authorization": f"Bearer {os.environ.get('BACKEND_API_KEY')}"
        }

        print(f"Calling refund endpoint: {refund_endpoint}")
        print(f"Refund payload: {json.dumps(payload, indent=2)}")

        # Test if the endpoint exists first with a HEAD request
        try:
            test_response = requests.head(refund_endpoint, timeout=10)
            if test_response.status_code == 404:
                print(f"❌ Refund endpoint not found (404). The backend may not have this endpoint implemented.")
                print(f"❌ Endpoint tested: {refund_endpoint}")
                return None
        except requests.exceptions.RequestException as e:
            print(f"⚠️  Could not test refund endpoint: {str(e)}")
            # Continue anyway in case HEAD requests are not supported

        response = requests.post(refund_endpoint, json=payload, headers=headers, timeout=30)

        if response.status_code == 200:
            refund_result = response.json()
            print(f"✅ Refund processed successfully: {refund_result}")
            return refund_result
        elif response.status_code == 404:
            print(f"❌ Refund endpoint not found (404): {refund_endpoint}")
            print(f"❌ This means the backend server doesn't have the refund API implemented.")
            print(f"❌ Please check if the backend has the '/api/aegisGrader/process-lambda-refunds' endpoint.")
            return None
        else:
            print(f"❌ Refund request failed with status {response.status_code}: {response.text}")
            print(f"❌ Endpoint: {refund_endpoint}")
            return None

    except requests.exceptions.Timeout:
        print("❌ Refund request timed out")
        return None
    except requests.exceptions.RequestException as e:
        print(f"❌ Refund request failed: {str(e)}")
        return None
    except Exception as e:
        print(f"❌ Unexpected error during refund processing: {str(e)}")
        return None

def process_files(data):
    answer_sheet_keys = []
    question_paper_key = None
    rubric_key = None

    for file in data['files']:
        purpose = file.get('filePurpose')
        key = file.get('key')
        if purpose == 'answer_sheet':
            answer_sheet_keys.append(key)
        elif purpose == 'question_paper':
            question_paper_key = key
        elif purpose == 'rubric':
            rubric_key = key

    return {
        "answer_sheet_keys": answer_sheet_keys,
        "question_paper_key": question_paper_key,
        "rubric_key": rubric_key
    }

def download_pdf(bucket, key):
    response = s3_client.get_object(Bucket=bucket, Key=key)
    pdf_bytes = response['Body'].read()
    return pdf_bytes

def lambda_handler(event, context):
    records = event["Records"]
    # print("Event => ", event)

    for record in records:
        body = json.loads(record["body"])
        s3 = body["Records"][0]["s3"]
        bucket = s3["bucket"]["name"]
        key = s3["object"]["key"]

        # Fetch and parse the manifest JSON from S3
        print(f"[Mehul] Debug got key: {key} in bucket: {bucket}")
        response = s3_client.get_object(Bucket=bucket, Key=key)
        # print("Response => ", response)
        content = response['Body'].read().decode('utf-8')
        data = json.loads(content)

        # Get the keys for the PDFs
        result = process_files(data)
        answer_sheet_keys = result["answer_sheet_keys"]
        question_paper_key = result["question_paper_key"]
        rubric_key = result["rubric_key"]

        # Download PDFs and store in variable (ram memory)
        pdfs = {}

        if question_paper_key:
            pdfs['question_paper'] = download_pdf(bucket, question_paper_key)
        if rubric_key:
            pdfs['rubric'] = download_pdf(bucket, rubric_key)
        pdfs['answer_sheets'] = []
        # answerSheetBytesTupleList = []
        for ans_key in answer_sheet_keys:
            pdfs['answer_sheets'].append((download_pdf(bucket, ans_key), ans_key))

        with ExitStack() as _:
            subject = data['testDetails']['subject']
            rubricFileName = doRubric(pdfs.get('rubric'), pdfs.get('question_paper'), subject)
            numThreads = 10
            evaluated_results = []
            answerSheetFilesBytesTuple = pdfs.get('answer_sheets', [])

            # Create the initial grading document
            document_id = create_grading_document(data)
            if not document_id:
                print("Failed to create initial grading document")
                return {"statusCode": 500, "body": json.dumps("Failed to create grading document")}

            # Counters for tracking results
            successful_count = 0
            error_count = 0

            # Define callback function to save results immediately
            def save_result_callback(result):
                nonlocal successful_count, error_count
                evaluation_text, answer_sheet_key = result

                # Track success/error counts
                if evaluation_text.startswith("Error:") or evaluation_text.startswith("ERROR"):
                    error_count += 1
                    print(f"⚠️  Error processing answer sheet {answer_sheet_key}: {evaluation_text}")
                else:
                    successful_count += 1
                    print(f"✅ Successfully processed answer sheet {answer_sheet_key}")

                # Append this answer sheet to the existing document immediately
                mongo_success = append_answer_sheet_to_document(document_id, data, result)
                if mongo_success:
                    print(f"✅ Successfully saved answer sheet to MongoDB document {document_id}")
                else:
                    print(f"❌ Failed to save answer sheet to MongoDB document {document_id}")

                evaluated_results.append(result[0])  # Keep the original list for return value

            # Create manager with callback for immediate saving
            man = Manager(doParallelAnswerSheet, list(zip(answerSheetFilesBytesTuple, [(rubricFileName, subject) for _ in range(len(answerSheetFilesBytesTuple))])), numThreads, result_callback=save_result_callback)
            results = man.processLoop()

            # Results are already processed by the callback, but we still get them here for completeness

            print(f"📊 Processing Summary for document {document_id}:")
            print(f"   ✅ Successful evaluations: {successful_count}")
            print(f"   ⚠️  Failed evaluations: {error_count}")
            print(f"   📄 Total answer sheets: {successful_count + error_count}")

            # Determine overall status
            total_sheets = successful_count + error_count
            if error_count == 0:
                overall_status = "completed"
            elif successful_count == 0:
                overall_status = "failed"
            else:
                overall_status = "partial_failure"

            # Process refunds for failed sheets if any
            refund_result = None
            if error_count > 0:
                print(f"💰 Processing refunds for {error_count} failed answer sheets...")
                refund_result = process_refunds_for_failed_sheets(data.get('creditInfo', {}), error_count)

            # Update the document with final statistics
            try:
                client = get_mongo_client()
                is_local = os.environ.get('AWS_SAM_LOCAL')
                db_name = 'AegisScholarTestDb' if is_local else 'AegisScholarDb'
                db = client[db_name]
                collection = db['AegisGrader']

                update_data = {
                    "$set": {
                        "processingStats.successfulEvaluations": successful_count,
                        "processingStats.failedEvaluations": error_count,
                        "processingStats.completedAt": datetime.now(),
                        "processingStats.overallStatus": overall_status,
                        "updatedAt": datetime.now()
                    }
                }

                # Update credit info if refund was processed
                if refund_result:
                    update_data["$set"]["creditInfo.creditsRefunded"] = refund_result.get('totalRefunded', 0)
                    if refund_result.get('refundTransactionIds'):
                        update_data["$push"] = {
                            "creditInfo.refundTransactionIds": {
                                "$each": refund_result.get('refundTransactionIds', [])
                            }
                        }

                collection.update_one({"_id": ObjectId(document_id)}, update_data)
                client.close()
                print(f"✅ Updated document {document_id} with final processing statistics and refund info")

                if refund_result:
                    print(f"💰 Refund Summary:")
                    print(f"   💸 Credits refunded: {refund_result.get('totalRefunded', 0)}")
                    print(f"   🧾 Refund transactions: {len(refund_result.get('refundTransactionIds', []))}")

            except Exception as e:
                print(f"⚠️  Failed to update document with final statistics: {str(e)}")

        return {"statusCode": 200, "body": json.dumps(evaluated_results)}

    return {"statusCode": 200}
